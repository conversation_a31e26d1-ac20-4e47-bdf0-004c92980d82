import Generator from "@/components/home/<USER>";
import { Metadata } from "next";
import { Nav } from "@/types/home/<USER>";
import { getTranslations } from "next-intl/server";

export const maxDuration = 120;

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations("");

  return {
    title: `${t("nav.create")} | ${t("metadata.title")}`,
    description: `${t("nav.create")} | ${t("metadata.description")}`,
    alternates: {
      canonical: `${process.env.NEXTAUTH_URL}/${
        locale !== "en" ? locale + "/" : ""
      }create`,
    },
  };
}

export default async function CreatePage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const t = await getTranslations("nav");

  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <div className="space-y-2">
          <h1 className="text-2xl font-semibold tracking-tight">
            {t("create")}
          </h1>
          <p className="text-sm text-muted-foreground"></p>
        </div>
      </div>

      <Generator />
    </div>
  );
}
